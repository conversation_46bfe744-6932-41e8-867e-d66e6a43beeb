
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  // Dynamically import lovable-tagger only in development mode
  let componentTagger;
  if (mode === 'development') {
    try {
      const lovableTagger = await import("lovable-tagger");
      componentTagger = lovableTagger.componentTagger;
    } catch (error) {
      console.warn('Could not load lovable-tagger:', error.message);
      componentTagger = null;
    }
  }

  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [
      react(),
      mode === 'development' && componentTagger && componentTagger(),
    ].filter(Bo<PERSON>an),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    optimizeDeps: {
      include: [
        '@tensorflow/tfjs',
        '@tensorflow/tfjs-backend-webgpu',
        '@tensorflow-models/pose-detection'
      ]
    },
  };
});
