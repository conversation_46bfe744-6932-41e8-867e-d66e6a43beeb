
import React, { useRef, useEffect, useState } from 'react';
import { useBlazePoseDetection } from '@/hooks/useBlazePoseDetection';
import { BLAZEPOSE_CONNECTIONS } from '@/shared/calculators/blazepose_constants';

interface SideViewBlazePoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  onPoseData?: (data: any) => void;
  userHeight?: { feet: number; inches: number };
}

const SideViewBlazePoseOverlay: React.FC<SideViewBlazePoseOverlayProps> = ({ 
  videoRef, 
  onPoseData,
  userHeight = { feet: 5, inches: 10 }
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [frameCount, setFrameCount] = useState(0);
  const [poseDataCount, setPoseDataCount] = useState(0);
  const [detectionActive, setDetectionActive] = useState(false);
  
  // Circuit breaker to prevent infinite loops
  const errorCountRef = useRef(0);
  const consecutiveErrorsRef = useRef(0);
  const MAX_CONSECUTIVE_ERRORS = 5;
  const [errorState, setErrorState] = useState<string | null>(null);
  
  const { detector, isInitialized, debugInfo, detectPoses } = useBlazePoseDetection('Full');

  // ENHANCED Side View BlazePose Custom Overlay - logging configuration
  const ENABLE_DETAILED_LOGGING = false; // Set to true only for debugging

  useEffect(() => {
    // ENHANCED Side View BlazePose Custom Overlay initialized (logging disabled for performance)

    // Custom BlazePose detector handles errors internally
    
    if (!isInitialized || !videoRef.current || !canvasRef.current) {
      if (ENABLE_DETAILED_LOGGING) {
        console.log('⏸️ Not ready for pose detection - initialized:', isInitialized, 'video:', !!videoRef.current, 'canvas:', !!canvasRef.current);
      }
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      console.error('❌ Failed to get canvas context');
      return;
    }

    if (ENABLE_DETAILED_LOGGING) {
      console.log('✅ Starting ENHANCED BlazePose Custom detection loop');
    }

    const updateCanvasSize = () => {
      if (!video || !canvas) return false;
      
      // Wait for video to have actual dimensions
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        return false;
      }
      
      // Get the video element's current display size
      const videoRect = video.getBoundingClientRect();
      
      if (videoRect.width === 0 || videoRect.height === 0) {
        return false;
      }
      
      // Set canvas size to match video display exactly
      canvas.width = videoRect.width;
      canvas.height = videoRect.height;
      
      // Position canvas to overlay video perfectly
      canvas.style.position = 'absolute';
      canvas.style.top = '0';
      canvas.style.left = '0';
      canvas.style.width = `${videoRect.width}px`;
      canvas.style.height = `${videoRect.height}px`;
      canvas.style.pointerEvents = 'none';
      canvas.style.zIndex = '10';
      canvas.style.background = 'transparent';
      
      console.log('🎨 CANVAS POSITIONING:', {
        canvasSize: `${canvas.width}x${canvas.height}`,
        videoRect: `${videoRect.width}x${videoRect.height}`,
        position: `${canvas.style.top}, ${canvas.style.left}`,
        zIndex: canvas.style.zIndex
      });
      
      return true;
    };

    const detectPose = async () => {
      try {
        // Check if video is ready to play
        if (!video || video.readyState < 3) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Check if video is paused or ended
        if (video.paused || video.ended) {
          setDetectionActive(false);
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        const currentFrame = frameCount + 1;
        setFrameCount(currentFrame);
        setDetectionActive(true);
        
        // Update canvas size - skip frame if not ready
        if (!updateCanvasSize()) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // CRITICAL: DO NOT CLEAR CANVAS - we need to see the debug indicators
        // ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // FORCE CANVAS DEBUG INDICATORS - MUCH LARGER AND BRIGHTER
        // Canvas debug indicators (logging disabled for performance)
        
        // Large red square - top left
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(0, 0, 100, 100);
        // Red square drawn
        
        // Large blue square - top right  
        ctx.fillStyle = '#0000FF';
        ctx.fillRect(canvas.width - 100, 0, 100, 100);
        // Blue square drawn

        // Large green square - bottom left
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(0, canvas.height - 100, 100, 100);
        // Green square drawn

        // Large yellow square - bottom right
        ctx.fillStyle = '#FFFF00';
        ctx.fillRect(canvas.width - 100, canvas.height - 100, 100, 100);
        // Yellow square drawn
        
        // Large animated center indicator
        const time = Date.now() * 0.001;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const animSize = 50 + Math.sin(time * 2) * 20;
        ctx.fillStyle = '#FF00FF';
        ctx.fillRect(centerX - animSize/2, centerY - animSize/2, animSize, animSize);
        // Animated center square drawn

        // Large frame counter
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 48px Arial';
        ctx.fillText(`FRAME ${currentFrame}`, 120, 80);
        // Frame counter drawn

        if (ENABLE_DETAILED_LOGGING) {
          console.log(`🎯 ENHANCED FRAME ${currentFrame}: Starting pose detection`);
        }
        
        // Detect poses using ENHANCED BlazePose Custom
        const poses = await detectPoses(video);
        
        console.log(`🎯 ENHANCED FRAME ${currentFrame}: BlazePose returned ${poses.length} poses`);
        
        if (poses && poses.length > 0) {
          const pose = poses[0];
          setPoseDataCount(prev => prev + 1);
          
          if (ENABLE_DETAILED_LOGGING) {
            console.log(`✅ ENHANCED POSE DETECTED - Frame ${currentFrame}:`);
            console.log('📊 Enhanced 2D Keypoints:', pose.keypoints?.length || 0);
            console.log('📊 Enhanced 3D Keypoints:', pose.keypoints3D?.length || 0);

            // Log enhanced keypoint coordinates with names
            if (pose.keypoints && pose.keypoints.length > 0) {
              console.log('📍 ENHANCED 2D COORDINATES (first 10 keypoints):');
              pose.keypoints.slice(0, 10).forEach((kp: any, idx: number) => {
                console.log(`  ${kp.name || idx}: x=${kp.x?.toFixed(3)}, y=${kp.y?.toFixed(3)}, score=${kp.score?.toFixed(3)}`);
              });
            }

            // Log enhanced 3D keypoint coordinates
            if (pose.keypoints3D && pose.keypoints3D.length > 0) {
              console.log('📍 ENHANCED 3D COORDINATES (first 10 keypoints):');
              pose.keypoints3D.slice(0, 10).forEach((kp: any, idx: number) => {
                console.log(`  ${kp.name || idx}: x=${kp.x?.toFixed(4)}, y=${kp.y?.toFixed(4)}, z=${kp.z?.toFixed(4)}`);
              });
            }
          }

          // Draw ENHANCED pose visualization with error isolation
          try {
            drawEnhancedPoseVisualization(ctx, pose, video, canvas);
          } catch (visualizationError) {
            console.warn('⚠️ Pose visualization error (non-critical):', visualizationError);
            // Continue processing - visualization errors should not stop pose detection
          }
          
          // Pass enhanced pose data to parent
          if (onPoseData) {
            onPoseData({
              frame: currentFrame,
              pose: pose,
              pipeline: 'BlazePose-Custom-Enhanced',
              userHeight: userHeight,
              coordinatesCount: {
                keypoints2D: pose.keypoints?.length || 0,
                keypoints3D: pose.keypoints3D?.length || 0
              },
              enhancement: 'tensor-processing-applied'
            });
          }
          
        } else {
          console.log(`❌ No poses detected on enhanced frame ${currentFrame}`);
        }
        
        // Reset consecutive errors on successful frame
        consecutiveErrorsRef.current = 0;
        setErrorState(null);
        
      } catch (error) {
        errorCountRef.current += 1;
        consecutiveErrorsRef.current += 1;
        
        console.error(`❌ Enhanced detection loop error (${consecutiveErrorsRef.current}/${MAX_CONSECUTIVE_ERRORS}):`, error);
        
        // Circuit breaker: stop after too many consecutive errors
        if (consecutiveErrorsRef.current >= MAX_CONSECUTIVE_ERRORS) {
          console.error('🚨 CIRCUIT BREAKER: Too many consecutive errors, stopping detection loop');
          setErrorState(`Circuit breaker triggered after ${MAX_CONSECUTIVE_ERRORS} consecutive errors`);
          setDetectionActive(false);
          return; // Stop the loop
        }
        
        // Add delay before retrying after error
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Continue animation loop only if no circuit breaker
      if (consecutiveErrorsRef.current < MAX_CONSECUTIVE_ERRORS) {
        animationFrameRef.current = requestAnimationFrame(detectPose);
      }
    };

    // Start detection with a small delay
    const startDelay = setTimeout(() => {
      // Starting ENHANCED pose detection loop
      detectPose();
    }, 1000);

    return () => {
      // Cleaning up ENHANCED Side View BlazePose detection
      clearTimeout(startDelay);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      setDetectionActive(false);
    };
  }, [isInitialized, videoRef, detectPoses, onPoseData, userHeight]);

  const drawEnhancedPoseVisualization = (
    ctx: CanvasRenderingContext2D,
    pose: any,
    video: HTMLVideoElement,
    canvas: HTMLCanvasElement
  ) => {
    try {
      const keypoints = pose.keypoints;
      if (!keypoints || keypoints.length === 0) return;
    
    // Scale keypoints from normalized coordinates to canvas coordinates
    const scaleX = canvas.width;
    const scaleY = canvas.height;
    
    if (ENABLE_DETAILED_LOGGING) {
      console.log('🎨 Drawing ENHANCED pose - scale factors:', {
        scaleX: scaleX,
        scaleY: scaleY,
        canvasSize: `${canvas.width}x${canvas.height}`,
        videoSize: `${video.videoWidth}x${video.videoHeight}`,
        coordinateSystem: 'normalized-to-canvas'
      });
    }
    
    // Draw keypoints as large, bright circles with names
    ctx.fillStyle = '#00FF00'; // Bright green
    ctx.strokeStyle = '#FFFFFF'; // White outline
    ctx.lineWidth = 3;
    
    let visiblePoints = 0;
    keypoints.forEach((keypoint: any, index: number) => {
      if (keypoint.score && keypoint.score > 0.3) {
        // Convert normalized coordinates to canvas coordinates
        const x = keypoint.x * scaleX;
        const y = keypoint.y * scaleY;
        
        // Draw large keypoint circle
        ctx.beginPath();
        ctx.arc(x, y, 12, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        
        // Draw keypoint name/number
        ctx.fillStyle = '#FF0000'; // Red text
        ctx.font = 'bold 12px Arial';
        const label = keypoint.name ? keypoint.name.substring(0, 8) : index.toString();
        ctx.fillText(label, x + 15, y - 10);
        ctx.fillStyle = '#00FF00'; // Reset to green
        
        visiblePoints++;
      }
    });
    
    if (ENABLE_DETAILED_LOGGING) {
      console.log('🎨 Drew', visiblePoints, 'ENHANCED visible keypoints out of', keypoints.length);
    }
    
    // Draw ENHANCED skeleton connections
    drawEnhancedSkeletonConnections(ctx, keypoints, scaleX, scaleY);
  };

  const drawEnhancedSkeletonConnections = (
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    try {
      // Use BlazePose standard connections
      ctx.strokeStyle = '#00FFFF'; // Bright cyan
      ctx.lineWidth = 4;
      ctx.lineCap = 'round';

      let connectionsDrawn = 0;
      BLAZEPOSE_CONNECTIONS.forEach(([startIdx, endIdx]) => {
        const start = keypoints[startIdx];
        const end = keypoints[endIdx];

        if (start && end && start.score > 0.3 && end.score > 0.3) {
          const startX = start.x * scaleX;
          const startY = start.y * scaleY;
          const endX = end.x * scaleX;
          const endY = end.y * scaleY;

          ctx.beginPath();
          ctx.moveTo(startX, startY);
          ctx.lineTo(endX, endY);
          ctx.stroke();
          connectionsDrawn++;
        }
      });

      if (ENABLE_DETAILED_LOGGING) {
        console.log('🦴 Drew', connectionsDrawn, 'ENHANCED skeleton connections');
      }
    } catch (error) {
      console.warn('⚠️ Error in pose visualization (gracefully handled):', error);
      // Graceful degradation - continue without visualization
    }
  };

  // Custom BlazePose detector handles errors internally - no error UI needed

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 10,
          border: '2px solid lime' // Visual indicator
        }}
      />
      
      <div 
        style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          background: 'rgba(0,0,0,0.9)',
          color: 'lime',
          padding: '12px',
          borderRadius: '6px',
          fontSize: '14px',
          zIndex: 11,
          border: '2px solid lime'
        }}
      >
        <div>🎯 BlazePose Full - ENHANCED</div>
        <div>📊 Frames: {frameCount}</div>
        <div>📊 Poses: {poseDataCount}</div>
        <div>🔧 Status: {detectionActive ? 'DETECTING' : 'WAITING'}</div>
        <div>🔧 {debugInfo || 'Initializing...'}</div>
        <div>👤 Height: {userHeight.feet}'{userHeight.inches}"</div>
        <div>📐 Canvas: {canvasRef.current ? `${canvasRef.current.width}x${canvasRef.current.height}` : 'Not ready'}</div>
        <div>🎥 Video: {videoRef.current ? `${videoRef.current.videoWidth}x${videoRef.current.videoHeight}` : 'Not ready'}</div>
        <div>⚡ Mode: Enhanced Tensor Processing</div>
        {errorState && (
          <div style={{ color: 'red', fontWeight: 'bold' }}>
            🚨 {errorState}
          </div>
        )}
      </div>
    </>
  );
};

export default SideViewBlazePoseOverlay;
