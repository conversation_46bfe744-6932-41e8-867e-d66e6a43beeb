
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { ImageSize } from './interfaces/common_interfaces';
import { getImageSize } from './image_utils';

/**
 * Converts various image sources to a standardized tensor format.
 * This is critical for consistent input processing across different image types.
 */
export function convertImageToTensor(
    image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement | ImageData | tf.Tensor3D,
    config: { outputTensorSize: { width: number; height: number }; keepAspectRatio: boolean; outputTensorFloatRange: number[]; borderMode: string },
    roi?: any): { imageTensor: tf.Tensor3D; padding: { top: number; bottom: number; left: number; right: number }; transformationMatrix?: number[][] } {
  
  // Removed excessive debug logging for production performance

  const imageSize = getImageSize(image);

  // Convert to tensor using TensorFlow.js
  let tensor = (image instanceof tf.Tensor) ? image : tf.browser.fromPixels(image);
  
  let padding = { top: 0, bottom: 0, left: 0, right: 0 };
  
  // Apply target size if specified
  const targetSize = config.outputTensorSize;
  if (targetSize) {
    
    if (config.keepAspectRatio) {
      // Calculate aspect ratio preserving resize
      const aspectRatio = imageSize.width / imageSize.height;
      const targetAspectRatio = targetSize.width / targetSize.height;
      
      let newWidth: number, newHeight: number;
      
      if (aspectRatio > targetAspectRatio) {
        // Image is wider than target
        newWidth = targetSize.width;
        newHeight = Math.round(targetSize.width / aspectRatio);
      } else {
        // Image is taller than target
        newHeight = targetSize.height;
        newWidth = Math.round(targetSize.height * aspectRatio);
      }
      
      // Removed excessive debug logging for production performance
      
      // Resize with aspect ratio preservation
      tensor = tf.image.resizeBilinear(tensor, [newHeight, newWidth]);
      
      // Pad to target size if needed
      if (newWidth !== targetSize.width || newHeight !== targetSize.height) {
        const padTop = Math.floor((targetSize.height - newHeight) / 2);
        const padBottom = targetSize.height - newHeight - padTop;
        const padLeft = Math.floor((targetSize.width - newWidth) / 2);
        const padRight = targetSize.width - newWidth - padLeft;
        
        padding = { top: padTop, bottom: padBottom, left: padLeft, right: padRight };
        tensor = tf.pad(tensor, [[padTop, padBottom], [padLeft, padRight], [0, 0]]);
      }
    } else {
      // Direct resize without aspect ratio preservation
      tensor = tf.image.resizeBilinear(tensor, [targetSize.height, targetSize.width]);
      console.log('🔧 IMAGE CONVERSION: Direct resize without aspect ratio preservation');
    }
  }
  
  // Apply normalization based on config
  let normalizedTensor = tensor;
  if (config.outputTensorFloatRange) {
    const [min, max] = config.outputTensorFloatRange;
    if (min === 0 && max === 1) {
      normalizedTensor = tf.div(tensor, 255.0);
    } else if (min === -1 && max === 1) {
      normalizedTensor = tf.sub(tf.div(tensor, 127.5), 1.0);
    }
    tensor.dispose(); // Clean up intermediate tensor
  }
  
  // Only log final tensor shape on errors or first run
  return { 
    imageTensor: normalizedTensor as tf.Tensor3D, 
    padding,
    transformationMatrix: [
      [1, 0, 0, 0],
      [0, 1, 0, 0],
      [0, 0, 1, 0],
      [0, 0, 0, 1]
    ]
  };
}

/**
 * Preprocesses image for BlazePose model input.
 * Handles model-specific requirements like normalization and sizing.
 */
export function preprocessImageForBlazePose(
    image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement | ImageData): tf.Tensor4D {
  
  // Removed excessive debug logging for production performance
  
  // FIXED: BlazePose Full model expects 224x224 input, not 256x256
  const modelInputSize = { width: 224, height: 224 };
  
  // Convert to tensor with model input size
  const config = {
    outputTensorSize: modelInputSize,
    keepAspectRatio: true,
    outputTensorFloatRange: [0, 1],
    borderMode: 'zero'
  };
  const result = convertImageToTensor(image, config);
  
  // Add batch dimension for model input
  const tensor4d = tf.expandDims(result.imageTensor, 0) as tf.Tensor4D;
  
  console.log('🔧 BLAZEPOSE PREPROCESSING: Preprocessed tensor shape:', tensor4d.shape);
  return tensor4d;
}

/**
 * Extracts ROI (Region of Interest) from image tensor.
 * This is useful for focusing processing on specific image regions.
 */
export function extractROIFromTensor(
    tensor: tf.Tensor3D,
    x: number,
    y: number,
    width: number,
    height: number): tf.Tensor3D {
  
  console.log('🔧 ROI EXTRACTION: Extracting ROI from tensor');
  console.log('🔧 ROI EXTRACTION: ROI bounds:', { x, y, width, height });
  
  // Ensure bounds are within tensor dimensions
  const [tensorHeight, tensorWidth] = tensor.shape.slice(0, 2);
  
  const clampedX = Math.max(0, Math.min(x, tensorWidth - 1));
  const clampedY = Math.max(0, Math.min(y, tensorHeight - 1));
  const clampedWidth = Math.min(width, tensorWidth - clampedX);
  const clampedHeight = Math.min(height, tensorHeight - clampedY);
  
  console.log('🔧 ROI EXTRACTION: Clamped bounds:', { 
    clampedX, clampedY, clampedWidth, clampedHeight 
  });
  
  // Extract the ROI slice
  const roiTensor = tf.slice(tensor, [clampedY, clampedX, 0], [clampedHeight, clampedWidth, -1]);
  
  console.log('🔧 ROI EXTRACTION: Extracted ROI shape:', roiTensor.shape);
  return roiTensor as tf.Tensor3D;
}
